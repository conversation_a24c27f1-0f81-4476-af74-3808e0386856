
import * as admin from 'firebase-admin';

// This file is for server-side code only (e.g., Server Actions, API routes).
// It initializes the Firebase Admin SDK, which has elevated privileges.

// Check if the app is already initialized to prevent errors
if (!admin.apps.length) {
  if (process.env.NODE_ENV === 'development') {
    // In development, we use the Firebase Emulators.
    // The Admin SDK needs to know the project ID to connect to the emulators.
    // Ensure you have `GCLOUD_PROJECT` set in your `.env.local` file.
    if (!process.env.GCLOUD_PROJECT) {
      throw new Error('GCLOUD_PROJECT environment variable is not set. This is required for the Admin SDK to connect to the emulators in development.');
    }
    admin.initializeApp({
      projectId: process.env.GCLOUD_PROJECT,
    });
  } else {
    // In production, the Admin SDK will automatically use the credentials
    // provided by the Google Cloud environment (e.g., Firebase App Hosting).
    admin.initializeApp();
  }
}

export const firestore = admin.firestore();
export const auth = admin.auth();
