
"use server";

import { revalidatePath } from "next/cache";
import { z } from "zod";
import { userSchema } from "./schemas";
import { firestore } from "@/lib/firebase/admin";

export async function addUser(data: z.infer<typeof userSchema>) {
  try {
    const validatedData = userSchema.parse(data);

    // Save the new user to the "users" collection in Firestore
    const userRef = await firestore.collection("users").add(validatedData);

    console.log(`User added with ID: ${userRef.id}`);

    // Revalidate the path to refresh the user list on the page
    revalidatePath("/admin");

    return {
      success: true,
      message: `Usuario "${validatedData.name}" creado exitosamente.`,
    };
  } catch (error) {
    console.error("Error adding user:", error);

    if (error instanceof z.ZodError) {
      return {
        success: false,
        message: "Datos inválidos. Por favor revise el formulario.",
      };
    }

    return {
      success: false,
      message: "Ocurrió un error al crear el usuario.",
    };
  }
}
