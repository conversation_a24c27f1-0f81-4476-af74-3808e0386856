"use client";

import * as React from "react";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Mail, ArrowLeft, Loader2 } from "lucide-react";
import { GastroKidEvalLogo } from "@/components/icons";
import { useToast } from "@/hooks/use-toast";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Terminal, CheckCircle } from "lucide-react";

export default function ForgotPasswordPage() {
  const { toast } = useToast();
  const [email, setEmail] = React.useState("");
  const [error, setError] = React.useState<string | null>(null);
  const [isLoading, setIsLoading] = React.useState(false);
  const [isSuccess, setIsSuccess] = React.useState(false);
  const [resetUrl, setResetUrl] = React.useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/auth/forgot-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Error al enviar el correo de recuperación');
      }

      setIsSuccess(true);

      // En desarrollo, guardar el enlace para mostrarlo
      if (data.resetUrl) {
        setResetUrl(data.resetUrl);
      }

      toast({
        title: "Correo enviado",
        description: "Se ha enviado un enlace de recuperación a su correo electrónico.",
      });

    } catch (error: any) {
      setError(error.message);
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (isSuccess) {
    return (
      <div className="w-full max-w-md mx-auto">
        <div className="p-1 rounded-2xl bg-gradient-to-br from-indigo-500 via-purple-500 to-pink-500">
          <div className="bg-card rounded-xl p-6 sm:p-8">
            <div className="text-center mb-8">
              <div className="inline-flex items-center justify-center bg-green-100 dark:bg-green-900/50 rounded-lg p-3 mb-4">
                <CheckCircle className="h-8 w-8 text-green-500 dark:text-green-400" />
              </div>
              <h1 className="text-3xl font-bold text-foreground">
                Correo Enviado
              </h1>
              <p className="text-muted-foreground mt-2 text-sm">
                Se ha enviado un enlace de recuperación a <strong>{email}</strong>
              </p>
            </div>

            <div className="space-y-4">
              <Alert>
                <Mail className="h-4 w-4" />
                <AlertTitle>Revise su correo electrónico</AlertTitle>
                <AlertDescription>
                  Hemos enviado un enlace de recuperación de contraseña a su correo electrónico.
                  El enlace expirará en 1 hora.
                </AlertDescription>
              </Alert>

              {/* Mostrar enlace directo en desarrollo */}
              {resetUrl && (
                <Alert className="border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950">
                  <Terminal className="h-4 w-4 text-blue-600" />
                  <AlertTitle className="text-blue-800 dark:text-blue-200">Modo Desarrollo</AlertTitle>
                  <AlertDescription className="text-blue-700 dark:text-blue-300">
                    <p className="mb-2">Como estás en modo desarrollo, aquí tienes el enlace directo:</p>
                    <Link
                      href={resetUrl}
                      className="inline-block bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors"
                    >
                      Ir a restablecer contraseña
                    </Link>
                  </AlertDescription>
                </Alert>
              )}

              <div className="text-center space-y-4">
                <p className="text-sm text-muted-foreground">
                  ¿No recibió el correo? Revise su carpeta de spam o intente nuevamente.
                </p>
                
                <Button
                  variant="outline"
                  onClick={() => {
                    setIsSuccess(false);
                    setEmail("");
                  }}
                  className="w-full"
                >
                  Enviar nuevamente
                </Button>

                <Link href="/login">
                  <Button variant="ghost" className="w-full">
                    <ArrowLeft className="mr-2 h-4 w-4" />
                    Volver al inicio de sesión
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full max-w-md mx-auto">
      <div className="p-1 rounded-2xl bg-gradient-to-br from-indigo-500 via-purple-500 to-pink-500">
        <div className="bg-card rounded-xl p-6 sm:p-8">
          <div className="text-center mb-8">
            <div className="inline-flex items-center justify-center bg-sky-100 dark:bg-sky-900/50 rounded-lg p-3 mb-4">
              <GastroKidEvalLogo className="h-8 w-8 text-sky-500 dark:text-sky-400" />
            </div>
            <h1 className="text-3xl font-bold text-foreground">
              Recuperar Contraseña
            </h1>
            <p className="text-muted-foreground mt-2 text-sm">
              Ingrese su correo electrónico para recibir un enlace de recuperación.
            </p>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            {error && (
              <Alert variant="destructive">
                <Terminal className="h-4 w-4" />
                <AlertTitle>Error</AlertTitle>
                <AlertDescription>
                  {error}
                </AlertDescription>
              </Alert>
            )}

            <div className="space-y-2">
              <Label htmlFor="email" className="font-semibold">Correo Electrónico</Label>
              <div className="relative">
                <Mail className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground" />
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  required
                  className="pl-10"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  disabled={isLoading}
                />
              </div>
            </div>
            
            <Button 
              type="submit" 
              size="lg" 
              className="w-full text-white font-bold bg-gradient-to-r from-blue-500 to-fuchsia-500 hover:from-blue-600 hover:to-fuchsia-600 shadow-lg transition-all" 
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Enviando...
                </>
              ) : (
                "Enviar Enlace de Recuperación"
              )}
            </Button>

            <div className="text-center">
              <Link href="/login">
                <Button variant="ghost" className="text-sm">
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Volver al inicio de sesión
                </Button>
              </Link>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
