// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

generator client {
  provider = "prisma-client-js"
}

model User {
  id        Int      @id @default(autoincrement())
  name      String
  email     String   @unique
  alias     String   @unique
  password  String
  role      String
  active    Boolean
  specialty String?
  msp       String?
  phone     String?
  createdAt DateTime @default(now())
  evaluations Evaluation[] @relation("UserEvaluations")
}

model Patient {
  id         Int      @id @default(autoincrement())
  alias      String   @unique
  name       String
  medicalRecords MedicalRecord[]
  parents     Parent[]
  appointments Appointment[]
  evaluations Evaluation[] @relation("PatientEvaluations")
}

model Parent {
  id         Int    @id @default(autoincrement())
  name       String
  patient    Patient @relation(fields: [patientId], references: [id])
  patientId  Int
}

model MedicalRecord {
  id            Int    @id @default(autoincrement())
  record_number String @unique
  patient       Patient @relation(fields: [patientId], references: [id])
  patientId     Int
  laboratoryExams LaboratoryExam[]
}

model LaboratoryExam {
  id             Int    @id @default(autoincrement())
  alias          String @unique
  medicalRecord  MedicalRecord @relation(fields: [medicalRecordId], references: [id])
  medicalRecordId Int
}

model Appointment {
  id         Int    @id @default(autoincrement())
  date       DateTime
  patient    Patient @relation(fields: [patientId], references: [id])
  patientId  Int
}

model Evaluation {
  id         Int    @id @default(autoincrement())
  alias      String @unique
  doctor     User   @relation("UserEvaluations", fields: [doctorId], references: [id])
  doctorId   Int
  patient    Patient @relation("PatientEvaluations", fields: [patientId], references: [id])
  patientId  Int
  evaluationLabExams EvaluationLabExam[]
}

model EvaluationLabExam {
  id           Int    @id @default(autoincrement())
  alias        String @unique
  evaluation   Evaluation @relation(fields: [evaluationId], references: [id])
  evaluationId Int
}
