"use client";

import * as React from "react";
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  useReactTable,
  SortingState,
  getSortedRowModel,
  ColumnFiltersState,
  Row,
} from "@tanstack/react-table";
import { MoreHorizontal, FileText, Pencil, Trash2, Search, X } from "lucide-react";
import Link from "next/link";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import type { Patient } from "@/lib/mock-data"; // This will be replaced with Prisma type later
import { EvaluationForm } from "../evaluations/new/evaluation-form";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";

const calculateAge = (dobString: string) => {
    const dob = new Date(dobString);
    const today = new Date();
    let age = today.getFullYear() - dob.getFullYear();
    const m = today.getMonth() - dob.getMonth();
    if (m < 0 || (m === 0 && today.getDate() < dob.getDate())) {
        age--;
    }
    return age;
};

// Mock data for previous evaluations - this will be replaced by an API call
import { type Evaluation, type EvaluationLabExam, type Patient } from "@prisma/client";

// Define a more specific type for our evaluations, including the doctor's name
type PopulatedEvaluation = Evaluation & {
  doctor: { name: string };
  evaluationLabExams: EvaluationLabExam[];
};

const PreviousEvaluationsPanel = ({ patientId, onSelectEvaluation }: { patientId: number, onSelectEvaluation: (evaluation: PopulatedEvaluation) => void }) => {
  const [evaluations, setEvaluations] = React.useState<PopulatedEvaluation[]>([]);
  const [selectedEvaluation, setSelectedEvaluation] = React.useState<PopulatedEvaluation | null>(null);
  const [isLoading, setIsLoading] = React.useState(true);

  React.useEffect(() => {
    const fetchEvaluations = async () => {
      try {
        setIsLoading(true);
        const response = await fetch(`/api/patients/${patientId}/evaluations`);
        if (!response.ok) {
          throw new Error("Failed to fetch evaluations");
        }
        const data = await response.json();
        setEvaluations(data);
        if (data.length > 0) {
          setSelectedEvaluation(data[0]);
          onSelectEvaluation(data[0]);
        }
      } catch (error) {
        console.error(error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchEvaluations();
  }, [patientId, onSelectEvaluation]);

  const handleSelect = (evaluation: PopulatedEvaluation) => {
    setSelectedEvaluation(evaluation);
    onSelectEvaluation(evaluation);
  }

  if (isLoading) {
    return (
        <Card className="flex flex-col h-full items-center justify-center">
            <p>Cargando historial...</p>
        </Card>
    );
  }

  return (
    <Card className="flex flex-col h-full">
      <CardHeader>
        <CardTitle>Consultas Anteriores</CardTitle>
        <CardDescription>Seleccione una consulta para ver los detalles y comparar.</CardDescription>
      </CardHeader>
      <CardContent className="flex-grow flex flex-col gap-4 overflow-hidden">
        <div className="space-y-2">
            <h3 className="font-semibold text-sm">Historial de Consultas</h3>
            {evaluations.length === 0 ? (
                <p className="text-sm text-muted-foreground">No se encontraron consultas anteriores.</p>
            ) : (
                <div className="flex flex-col gap-2">
                    {evaluations.map((evalItem) => (
                        <Button 
                            key={evalItem.id}
                            variant={selectedEvaluation?.id === evalItem.id ? "secondary" : "outline"}
                            onClick={() => handleSelect(evalItem)}
                            className="w-full justify-start"
                        >
                            <FileText className="mr-2 h-4 w-4" />
                            {new Date(evalItem.createdAt).toLocaleDateString()} - Dr. {evalItem.doctor.name}
                        </Button>
                    ))}
                </div>
            )}
        </div>
        
        {selectedEvaluation && (
            <div className="flex-grow flex flex-col border rounded-lg p-4 bg-muted/20 overflow-hidden">
                <h3 className="font-bold text-lg mb-2">Detalles de la Consulta</h3>
                <ScrollArea className="flex-grow pr-4">
                    <div className="space-y-4">
                        <p><strong>Fecha:</strong> {new Date(selectedEvaluation.createdAt).toLocaleString()}</p>
                        <p><strong>Médico:</strong> Dr. {selectedEvaluation.doctor.name}</p>
                        <p><strong>Motivo:</strong> {selectedEvaluation.reason || "No especificado"}</p>
                        <div>
                            <h4 className="font-semibold mb-2">Exámenes Adjuntos</h4>
                            {selectedEvaluation.evaluationLabExams.length > 0 ? (
                                <ul className="space-y-2">
                                    {selectedEvaluation.evaluationLabExams.map((file) => (
                                        <li key={file.id} className="flex items-center justify-between text-sm p-2 rounded-md bg-background">
                                            <a href={file.url} target="_blank" rel="noopener noreferrer" className="hover:underline">{file.name}</a>
                                            <Badge variant="outline">{file.category}</Badge>
                                        </li>
                                    ))}
                                </ul>
                            ) : (
                                <p className="text-sm text-muted-foreground">No hay exámenes adjuntos.</p>
                            )}
                        </div>
                    </div>
                </ScrollArea>
            </div>
        )}
      </CardContent>
    </Card>
  );
};


const NewEvaluationPanel = ({ patient, previousEvaluation }: { patient: Patient, previousEvaluation: PopulatedEvaluation | null }) => {
  return (
    <Card className="h-full">
        <ScrollArea className="h-full">
            <div className="p-6">
                <EvaluationForm patientData={patient} previousEvaluationData={previousEvaluation} />
            </div>
        </ScrollArea>
    </Card>
  );
};

export const columns: ColumnDef<Patient>[] = [
  {
    accessorKey: "recordNumber",
    header: "ID",
  },
  {
    accessorKey: "name",
    header: "Nombre Completo",
  },
   {
    id: "age",
    header: "Edad",
    cell: ({ row }) => calculateAge(row.original.dob),
  },
  {
    accessorKey: "gender",
    header: "Género",
  },
  {
    accessorKey: "dob",
    header: "Fecha de Nacimiento",
    cell: ({ row }) => {
        const [year, month, day] = (row.getValue("dob") as string).split('-').map(Number);
        return `${day}/${month}/${year}`;
    }
  },
  {
    accessorKey: "idNumber",
    header: "N° Identificación",
  },
  {
    id: "actions",
    header: "Acciones",
    cell: ({ row }) => {
      return (
        <div className="text-right">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Abrir menú</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem>
                <FileText className="mr-2 h-4 w-4" />
                <span>Ver Expediente</span>
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Pencil className="mr-2 h-4 w-4" />
                <span>Editar Paciente</span>
              </DropdownMenuItem>
              <DropdownMenuItem className="text-destructive focus:text-destructive">
                <Trash2 className="mr-2 h-4 w-4" />
                <span>Eliminar</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      );
    },
  },
];

interface PatientTableProps<TData> {
  data: TData[];
  onRowSelect: (row: Row<TData>) => void;
}

export function PatientTable<TData extends Patient>({ data, onRowSelect }: PatientTableProps<TData>) {
  const [sorting, setSorting] = React.useState<SortingState>([])
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([])
  const [globalFilter, setGlobalFilter] = React.useState('')

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    onColumnFiltersChange: setColumnFilters,
    getFilteredRowModel: getFilteredRowModel(),
    onGlobalFilterChange: setGlobalFilter,
    state: {
      sorting,
      columnFilters,
      globalFilter,
    },
  });

  return (
    <div className="space-y-4">
        <h1 className="text-3xl font-bold tracking-tight">Gestión de Pacientes</h1>
        <p className="text-muted-foreground">
            Busque y seleccione un paciente para iniciar una nueva evaluación o revisar su historial.
        </p>
      <div className="relative">
        <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="Buscar por nombre, ID o número de identificación..."
          value={globalFilter ?? ""}
          onChange={(event) => setGlobalFilter(event.target.value)}
          className="max-w-sm pl-10"
        />
      </div>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id} className="hover:bg-transparent border-b">
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id} className="text-muted-foreground uppercase text-xs">
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                  onClick={() => onRowSelect(row)}
                  className="cursor-pointer hover:bg-muted/50"
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id} className="font-medium py-3">
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No se encontraron resultados.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <div className="flex items-center justify-end space-x-2 py-4">
        <Button
          variant="outline"
          size="sm"
          onClick={() => table.previousPage()}
          disabled={!table.getCanPreviousPage()}
        >
          Anterior
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => table.nextPage()}
          disabled={!table.getCanNextPage()}
        >
          Siguiente
        </Button>
      </div>
    </div>
  );
}


export default function PatientsPage({ data }: { data: Patient[] }) {
  const [selectedPatient, setSelectedPatient] = React.useState<Patient | null>(null);
  const [selectedEvaluation, setSelectedEvaluation] = React.useState<PopulatedEvaluation | null>(null);

  const handleRowSelect = (row: Row<Patient>) => {
    setSelectedPatient(row.original);
  };

  const handleCloseComparison = () => {
    setSelectedPatient(null);
    setSelectedEvaluation(null);
  };

  if (selectedPatient) {
    return (
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-2xl font-bold">Comparando Evaluaciones</h2>
            <p className="text-muted-foreground">Paciente: {selectedPatient.name}</p>
          </div>
          <Button variant="outline" onClick={handleCloseComparison}>
            <X className="mr-2 h-4 w-4" />
            Cerrar y Volver al Listado
          </Button>
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 h-[calc(100vh-150px)]">
          <PreviousEvaluationsPanel patientId={selectedPatient.id} onSelectEvaluation={setSelectedEvaluation} />
          <NewEvaluationPanel patient={selectedPatient} previousEvaluation={selectedEvaluation} />
        </div>
      </div>
    );
  }

  return <PatientTable data={data} onRowSelect={handleRowSelect} />;
}
