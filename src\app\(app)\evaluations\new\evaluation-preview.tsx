
"use client";

import * as React from "react";
import type * as z from "zod";
import { format } from "date-fns";
import { es } from "date-fns/locale";

import type { evaluationSchema } from "./schemas";
import { GastroKidEvalLogo } from "@/components/icons";
import { GrowthCharts } from "./growth-charts";

interface EvaluationPreviewProps {
  data: z.infer<typeof evaluationSchema>;
}

// Helper component for sections
const Section: React.FC<{
  title: string;
  children: React.ReactNode;
  className?: string;
}> = ({ title, children, className }) => (
  <div className={className}>
    <h3 className="font-bold text-lg border-b-2 border-gray-400 pb-1 mb-3">
      {title}
    </h3>
    {children}
  </div>
);

// Helper for data fields
const DataField: React.FC<{ label: string; value: React.ReactNode }> = ({
  label,
  value,
}) => (
  <div className="flex flex-col mb-3">
    <p className="text-sm font-semibold text-gray-600">{label}</p>
    <p className="text-base whitespace-pre-wrap">
      {value || <span className="text-gray-400">N/A</span>}
    </p>
  </div>
);

const TwoColumn: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <div className="flex flex-wrap -mx-3">{children}</div>
);

const Column: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <div className="w-full md:w-1/2 px-3">{children}</div>
);

export function EvaluationPreview({ data }: EvaluationPreviewProps) {
  const doctorInfo = {
    name: "Dr. Ejemplo Gastro",
    specialty: "Gastroenterología Pediátrica",
    msp: "MSP-12345",
    address: "Av. Principal 123, Consultorio 404, Quito",
    phone: "************",
  };

  const { appointment, patient, mother, father, medical } = data;

  const showGrowthCharts =
    patient.gender &&
    patient.dob &&
    (medical.weight || medical.height || medical.headCircumference);

  return (
    <div
      id="evaluation-pdf-content"
      className="bg-white text-black p-10 font-serif"
      style={{ width: "794px" }} // A4 paper width
    >
      <header className="flex justify-between items-start pb-6 border-b-4 border-sky-600">
        <div>
          <h1 className="text-4xl font-bold text-gray-800">
            {doctorInfo.name}
          </h1>
          <p className="text-xl text-gray-700">{doctorInfo.specialty}</p>
          <div className="text-sm text-gray-600 mt-2">
            <p>{doctorInfo.address}</p>
            <p>
              Tel: {doctorInfo.phone} | Reg. MSP: {doctorInfo.msp}
            </p>
          </div>
        </div>
        <GastroKidEvalLogo className="size-20 text-sky-600" />
      </header>

      <main className="pt-6 text-base">
        <h2 className="text-2xl font-bold text-center uppercase tracking-wider mb-6 text-sky-700">
          Informe de Evaluación Médica
        </h2>

        <Section title="Datos de la Cita">
          <TwoColumn>
            <Column>
              <DataField
                label="Fecha de Cita"
                value={
                  appointment.date
                    ? format(appointment.date, "PPP", { locale: es })
                    : "N/A"
                }
              />
            </Column>
            <Column>
              <DataField label="Hora" value={appointment.time} />
            </Column>
            <Column>
              <DataField
                label="Nº de Expediente"
                value={appointment.recordNumber}
              />
            </Column>
            <Column>
              <DataField label="Seguro" value={appointment.insurance} />
            </Column>
            <Column>
              <DataField label="Pediatra" value={appointment.pediatrician} />
            </Column>
            <Column>
              <DataField label="Referido por" value={appointment.referrer} />
            </Column>
          </TwoColumn>
        </Section>

        <Section title="Datos del Paciente" className="mt-4">
          <TwoColumn>
            <Column>
              <DataField label="Nombre Completo" value={patient.name} />
            </Column>
            <Column>
              <DataField label="Nº de Cédula" value={patient.idCard} />
            </Column>
            <Column>
              <DataField
                label="Fecha de Nacimiento"
                value={
                  patient.dob
                    ? format(patient.dob, "PPP", { locale: es })
                    : "N/A"
                }
              />
            </Column>
            <Column>
              <DataField label="Edad" value={patient.age} />
            </Column>
            <Column>
              <DataField label="Sexo" value={patient.gender} />
            </Column>
          </TwoColumn>
        </Section>

        <Section title="Datos de los Padres" className="mt-4">
          <TwoColumn>
            <Column>
              <h4 className="font-semibold text-md mb-2">Madre</h4>
              <DataField label="Nombre" value={mother.name} />
              <DataField label="Teléfono" value={mother.phone} />
              <DataField label="Ocupación" value={mother.occupation} />
            </Column>
            <Column>
              <h4 className="font-semibold text-md mb-2">Padre</h4>
              <DataField label="Nombre" value={father.name} />
              <DataField label="Teléfono" value={father.phone} />
              <DataField label="Ocupación" value={father.occupation} />
            </Column>
          </TwoColumn>
        </Section>

        <div className="mt-6 pt-6 border-t-2 border-dashed border-gray-400">
          <h2 className="text-xl font-bold text-center uppercase tracking-wider mb-6 text-sky-700">
            Evaluación Médica Detallada
          </h2>

          <Section title="Información Clínica">
            <DataField
              label="Motivo de Consulta"
              value={medical.consultationReason}
            />
            <DataField
              label="Enfermedad Actual"
              value={medical.currentIllness}
            />
            <DataField
              label="Síntomas Digestivos Superiores"
              value={medical.upperDigestiveSymptoms}
            />
            <DataField
              label="Síntomas Digestivos Inferiores"
              value={medical.lowerDigestiveSymptoms}
            />
            <DataField
              label="Hábitos Intestinales"
              value={medical.bowelHabits}
            />
          </Section>

          <Section
            title="Datos Antropométricos y Signos Vitales"
            className="mt-4"
          >
            <TwoColumn>
              <Column>
                <DataField label="Peso (kg)" value={medical.weight} />
              </Column>
              <Column>
                <DataField label="Talla (cm)" value={medical.height} />
              </Column>
              <Column>
                <DataField
                  label="Perímetro Cefálico (cm)"
                  value={medical.headCircumference}
                />
              </Column>
              <Column>
                <DataField
                  label="Temperatura (°C)"
                  value={medical.temperature}
                />
              </Column>
              <Column>
                <DataField
                  label="Frec. Cardiaca (lpm)"
                  value={medical.cardiacFrequency}
                />
              </Column>
              <Column>
                <DataField
                  label="Presión Arterial"
                  value={medical.bloodPressure}
                />
              </Column>
              <Column>
                <DataField
                  label="Sat. de Oxígeno (%)"
                  value={medical.oxygenSaturation}
                />
              </Column>
            </TwoColumn>
          </Section>

          <Section
            title="Historia del Paciente"
            className="mt-4 page-break-before"
          >
            <DataField
              label="Historia Perinatal"
              value={medical.perinatalHistory}
            />
            <DataField
              label="Historia Nutricional"
              value={medical.nutritionalHistory}
            />
            <DataField
              label="Historia del Desarrollo"
              value={medical.developmentHistory}
            />
            <DataField label="Inmunizaciones" value={medical.immunizations} />
            <DataField
              label="Antecedentes Médicos Personales"
              value={medical.personalMedicalHistory}
            />
            <DataField
              label="Antecedentes Médicos Familiares"
              value={medical.familyMedicalHistory}
            />
          </Section>

          <Section title="Examen Físico y Plan" className="mt-4">
            <DataField
              label="Observaciones Generales"
              value={medical.generalObservations}
            />
            <DataField
              label="Revisión por Sistemas"
              value={medical.systemsReview}
            />
            <DataField label="Examen Físico" value={medical.physicalExam} />
            <DataField
              label="Exámenes Paraclínicos"
              value={medical.paraclinical}
            />
            <DataField
              label="Impresiones Diagnósticas"
              value={medical.diagnosticImpression}
            />
            <DataField label="Plan de Acción" value={medical.actionPlan} />
          </Section>
        </div>
        {showGrowthCharts && (
          <Section
            title="Curvas de Crecimiento"
            className="mt-4 pt-4 page-break-before"
          >
            <GrowthCharts
              sex={patient.gender}
              dob={patient.dob}
              weight={medical.weight}
              height={medical.height}
              headCircumference={medical.headCircumference}
            />
          </Section>
        )}
      </main>

      <footer className="mt-24 text-center">
        <div className="w-2/3 mx-auto">
          <div className="border-t-2 border-black pt-2">
            <p className="text-sm">Firma del Médico</p>
            <p className="font-bold mt-2">{doctorInfo.name}</p>
            <p className="text-sm">{doctorInfo.specialty}</p>
            <p className="text-sm">Reg. MSP: {doctorInfo.msp}</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
