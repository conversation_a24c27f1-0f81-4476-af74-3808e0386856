'use client'

import { createContext, useContext, useState, useEffect } from 'react'
import { UserSession } from '@/types/session'



type SessionContextType = {
  user: UserSession | null
  isAuthenticated: boolean
}

const SessionContext = createContext<SessionContextType>({
  user: null,
  isAuthenticated: false,
})

export function useSession() {
  return useContext(SessionContext)
}

export function SessionProvider({
  children,
  initialUser,
}: {
  children: React.ReactNode
  initialUser: UserSession | null
}) {
  const [user, setUser] = useState(initialUser)

  useEffect(() => {
    // En caso de que quieras refrescar desde el cliente más adelante
    setUser(initialUser)
  }, [initialUser])

  return (
    <SessionContext.Provider
      value={{ user, isAuthenticated: !!user }}
    >
      {children}
    </SessionContext.Provider>
  )
}