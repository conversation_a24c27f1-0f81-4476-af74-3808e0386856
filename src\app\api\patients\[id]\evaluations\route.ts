import { NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function GET(request: Request, { params }: { params: { id: string } }) {
  const patientId = parseInt(params.id, 10);

  if (isNaN(patientId)) {
    return NextResponse.json({ error: 'Invalid patient ID' }, { status: 400 });
  }

  try {
    const evaluations = await prisma.evaluation.findMany({
      where: {
        patientId: patientId,
      },
      include: {
        evaluationLabExams: true, // Include related lab exams
        doctor: {
          select: {
            name: true,
          }
        }
      },
      orderBy: {
        // Assuming you add a date field to the Evaluation model
        // createdAt: 'desc',
      },
    });

    if (!evaluations) {
      return NextResponse.json({ error: 'No evaluations found for this patient' }, { status: 404 });
    }

    return NextResponse.json(evaluations);
  } catch (error) {
    console.error('Error fetching evaluations:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
