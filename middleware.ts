import { NextRequest, NextResponse } from 'next/server'
import { verifyToken } from '@/lib/auth/auth'

export function middleware(req: NextRequest) {
  const token = req.cookies.get('token')?.value

  // Si no hay token, redirige al login
  if (!token) {
    return NextResponse.redirect(new URL('/login', req.url))
  }

  try {
    const decoded = verifyToken(token) as any

    // Ejemplo: permitir solo usuarios activos
    if (!decoded || decoded.role !== 'admin') {
      return NextResponse.redirect(new URL('/unauthorized', req.url))
    }

    // Todo bien: continuar
    return NextResponse.next()
  } catch (err) {
    return NextResponse.redirect(new URL('/login', req.url))
  }
}

export const config = {
  matcher: ['/app/:path*'],
}