import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { FilePlus2, Users, LogOut } from "lucide-react";

export default function DashboardPage() {
  return (
    <div className="flex flex-col gap-8">
      <div className="flex flex-col sm:flex-row justify-between sm:items-center gap-4">
        <div>
            <h1 className="text-3xl font-bold font-headline">
            Bienvenido a GastroKid Eval
            </h1>
            <p className="text-muted-foreground mt-1">
            Su solución integral para evaluaciones de gastroenterología pediátrica.
            </p>
        </div>
        <Link href="/login">
            <Button variant="outline" className="w-full sm:w-auto">
                <LogOut className="mr-2 h-4 w-4" />
                Cerrar <PERSON>
            </Button>
        </Link>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <Card className="shadow-sm hover:shadow-md transition-shadow">
          <CardHeader>
            <div className="flex justify-between items-start">
                <CardTitle className="text-xl font-semibold">Nueva Evaluación</CardTitle>
                <FilePlus2 className="h-6 w-6 text-muted-foreground" />
            </div>
          </CardHeader>
          <CardContent>
            <CardDescription className="mb-6">
              Iniciar un nuevo proceso de evaluación de paciente.
            </CardDescription>
            <Link href="/evaluations/new">
              <Button>
                Crear Evaluación
              </Button>
            </Link>
          </CardContent>
        </Card>

        <Card className="shadow-sm hover:shadow-md transition-shadow">
          <CardHeader>
             <div className="flex justify-between items-start">
                <CardTitle className="text-xl font-semibold">Registros de Pacientes</CardTitle>
                <Users className="h-6 w-6 text-muted-foreground" />
            </div>
          </CardHeader>
          <CardContent>
            <CardDescription className="mb-6">
             Gestionar y buscar registros de pacientes existentes.
            </CardDescription>
            <Link href="/patients">
              <Button>
                Ver Pacientes
              </Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
