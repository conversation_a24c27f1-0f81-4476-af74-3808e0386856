
import * as z from "zod";

const personSchema = z.object({
  name: z.string().min(2, "El nombre completo es requerido."),
  age: z.string().optional(),
  address: z.string().optional(),
  phone: z.string().optional(),
  occupation: z.string().optional(),
});

export const evaluationSchema = z.object({
  appointment: z.object({
    date: z.date({ required_error: "La fecha de la cita es requerida." }),
    time: z.string().min(1, "La hora es requerida."),
    recordNumber: z.string().min(1, "El número de expediente es requerido."),
    insurance: z.string().optional(),
    pediatrician: z.string().optional(),
    referrer: z.string().optional(),
  }),
  patient: z.object({
    name: z.string().min(2, "El nombre completo es requerido."),
    idCard: z.string().optional(),
    gender: z.string().min(1, "El sexo es requerido."),
    dob: z.date({ required_error: "La fecha de nacimiento es requerida." }),
    age: z.string().optional(),
  }),
  mother: person<PERSON><PERSON><PERSON>,
  father: person<PERSON>che<PERSON>,
  medical: z.object({
    consultationReason: z.string().min(1, "El motivo de consulta es requerido."),
    currentIllness: z.string().min(1, "La descripción de la enfermedad es requerida."),
    upperDigestiveSymptoms: z.string().optional(),
    lowerDigestiveSymptoms: z.string().optional(),
    bowelHabits: z.string().optional(),
    weight: z.string().optional(),
    height: z.string().optional(),
    headCircumference: z.string().optional(),
    bloodPressure: z.string().optional(),
    temperature: z.string().optional(),
    cardiacFrequency: z.string().optional(),
    oxygenSaturation: z.string().optional(),
    
    // Historia del Paciente
    perinatalHistory: z.string().optional(),
    nutritionalHistory: z.string().optional(),
    developmentHistory: z.string().optional(),
    immunizations: z.string().optional(),
    personalMedicalHistory: z.string().optional(),
    familyMedicalHistory: z.string().optional(),
    
    // Examen y Plan
    generalObservations: z.string().optional(),
    systemsReview: z.string().optional(),
    physicalExam: z.string().optional(),
    paraclinical: z.string().optional(),
    diagnosticImpression: z.string().optional(),
    actionPlan: z.string().optional(),
  }),
  labFiles: z.array(z.object({
    file: z.any(),
    category: z.string(),
  })).optional(),
});
