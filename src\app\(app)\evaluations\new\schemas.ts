
import * as z from "zod";

const personSchema = z.object({
  name: z.string().min(2, "El nombre completo es requerido."),
  age: z.string().optional(),
  address: z.string().optional(),
  phone: z.string().optional(),
  occupation: z.string().optional(),
});

export const evaluationSchema = z.object({
  appointment: z.object({
    date: z.preprocess(
      (val) => typeof val === "string" ? new Date(val) : val,
      z.date({ required_error: "La fecha de la cita es requerida." })
    ),
    time: z.string().min(1, "La hora es requerida."),
    recordNumber: z.string().min(1, "El número de expediente es requerido."),
    insurance: z.string().optional(),
    pediatrician: z.string().optional(),
    referrer: z.string().optional(),
  }),
  patient: z.object({
    name: z.string().min(2, "El nombre completo es requerido."),
    idCard: z.string().optional(),
    gender: z.string().min(1, "El sexo es requerido."),
    dob: z.preprocess(
      (val) => typeof val === "string" ? new Date(val) : val,
      z.date({ required_error: "La fecha de nacimiento es requerida." })
    ),
    age: z.string().optional(),
  }),
  mother: personSchema,
  father: personSchema,
  medical: z.object({
    consultationReason: z.string().min(1, "El motivo de consulta es requerido."),
    currentIllness: z.string().min(1, "La descripción de la enfermedad es requerida."),
    upperDigestiveSymptoms: z.string().optional(),
    lowerDigestiveSymptoms: z.string().optional(),
    bowelHabits: z.string().optional(),
    weight: z.preprocess(
      (val) => (val === "" ? null : Number(val)),
      z.number().nullable().optional()
    ),
    height: z.preprocess(
      (val) => (val === "" ? null : Number(val)),
      z.number().nullable().optional()
    ),
    headCircumference: z.preprocess(
      (val) => (val === "" ? null : Number(val)),
      z.number().nullable().optional()
    ),
    bloodPressure: z.string().optional(),
    temperature: z.preprocess(
      (val) => (val === "" ? null : Number(val)),
      z.number().nullable().optional()
    ),
    cardiacFrequency: z.preprocess(
      (val) => (val === "" ? null : Number(val)),
      z.number().nullable().optional()
    ),
    oxygenSaturation: z.preprocess(
      (val) => (val === "" ? null : Number(val)),
      z.number().nullable().optional()
    ),
    
    // Historia del Paciente
    perinatalHistory: z.string().optional(),
    nutritionalHistory: z.string().optional(),
    developmentHistory: z.string().optional(),
    immunizations: z.string().optional(),
    personalMedicalHistory: z.string().optional(),
    familyMedicalHistory: z.string().optional(),
    
    // Examen y Plan
    generalObservations: z.string().optional(),
    systemsReview: z.string().optional(),
    physicalExam: z.string().optional(),
    paraclinical: z.string().optional(),
    diagnosticImpression: z.string().optional(),
    actionPlan: z.string().optional(),
  }),
  labFiles: z.array(z.object({
    name: z.string(),
    category: z.string(),
    url: z.string(),
  })).optional(),
  userId: z.number(),
});
