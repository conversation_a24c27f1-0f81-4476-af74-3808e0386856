
"use client";

import * as React from "react";
import { differenceInMonths } from "date-fns";
import {
  Line,
  XAxis,
  <PERSON>A<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Legend,
  ComposedChart,
} from "recharts";
import {
  ChartContainer,
} from "@/components/ui/chart";
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { growthPercentiles } from "@/lib/growth-chart-data";

interface GrowthChartsProps {
  sex?: "masculino" | "femenino" | string;
  dob?: Date;
  weight?: string;
  height?: string;
  headCircumference?: string;
}

const chartConfig = {
  p3: { label: "P3", color: "hsl(var(--chart-1))" },
  p15: { label: "P15", color: "hsl(var(--chart-2))" },
  p50: { label: "P50", color: "hsl(var(--chart-3))" },
  p85: { label: "P85", color: "hsl(var(--chart-4))" },
  p97: { label: "P97", color: "hsl(var(--chart-5))" },
  patient: { label: "Paciente", color: "hsl(var(--destructive))" },
};

const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    const data = payload[0].payload;
    const unit = payload[0].dataKey.includes("weight") ? "kg" : "cm";
    const ageInMonths = parseInt(label);
    const ageDisplay = ageInMonths >= 24
      ? `${Math.floor(ageInMonths / 12)} años ${ageInMonths % 12} meses`
      : `${ageInMonths} meses`;

    return (
      <div className="p-2 bg-background border rounded-md shadow-lg">
        <p className="font-bold">{`Edad: ${ageDisplay}`}</p>
        {payload.map((p: any) => (
          <p key={p.name} style={{ color: p.color }}>
            {`${p.name}: ${p.value.toFixed(2)} ${unit}`}
          </p>
        ))}
         {data.patient && (
          <p style={{ color: 'red', fontWeight: 'bold' }}>
            {`Paciente: ${data.patient.toFixed(2)} ${unit}`}
          </p>
        )}
      </div>
    );
  }
  return null;
};


export function GrowthCharts({
  sex,
  dob,
  weight,
  height,
  headCircumference,
}: GrowthChartsProps) {
  console.log("🔄 GrowthCharts component rendering...");
  console.log("GrowthCharts props:", { sex, dob, weight, height, headCircumference });

  // Versión simplificada para debug
  return (
    <div className="space-y-4 p-4 border-2 border-blue-500 rounded-lg">
      <div className="bg-blue-100 p-4 rounded">
        <h3 className="font-bold text-blue-900">🔄 Componente GrowthCharts Renderizado</h3>
        <div className="mt-2 text-sm">
          <p><strong>Sexo:</strong> {sex || "No definido"}</p>
          <p><strong>Fecha de nacimiento:</strong> {dob ? new Date(dob).toLocaleDateString() : "No definida"}</p>
          <p><strong>Peso:</strong> {weight || "No definido"}</p>
          <p><strong>Talla:</strong> {height || "No definida"}</p>
          <p><strong>Perímetro cefálico:</strong> {headCircumference || "No definido"}</p>
        </div>
      </div>

      {(!sex || !dob) ? (
        <div className="text-center p-8 text-muted-foreground bg-yellow-50 rounded">
          <p>Para mostrar las curvas de crecimiento necesitas:</p>
          <ul className="list-disc list-inside mt-2">
            <li>Seleccionar el sexo del paciente</li>
            <li>Ingresar la fecha de nacimiento</li>
            <li>Ingresar al menos un valor (peso, talla o perímetro cefálico)</li>
          </ul>
        </div>
      ) : (
        <div className="bg-green-100 p-4 rounded">
          <p className="text-green-800">✅ Datos suficientes para mostrar curvas de crecimiento</p>
          <p className="text-sm text-green-600 mt-1">Las curvas aparecerían aquí...</p>
        </div>
      )}
    </div>
  );
}


