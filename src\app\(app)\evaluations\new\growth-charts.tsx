
"use client";

import * as React from "react";
import { differenceInMonths } from "date-fns";
import {
  Line,
  XAxis,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Legend,
  ComposedChart,
} from "recharts";
import {
  ChartContainer,
} from "@/components/ui/chart";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { growthPercentiles } from "@/lib/growth-chart-data";

interface GrowthChartsProps {
  sex?: "masculino" | "femenino" | string;
  dob?: Date;
  currentWeight?: string;
  currentHeight?: string;
  currentHeadCircumference?: string;
  previousEvaluation?: {
    createdAt: string | Date;
    weight?: number | null;
    height?: number | null;
    headCircumference?: number | null;
  } | null;
}

const chartConfig = {
  p3: { label: "P3", color: "hsl(var(--chart-1))" },
  p15: { label: "P15", color: "hsl(var(--chart-2))" },
  p50: { label: "P50", color: "hsl(var(--chart-3))" },
  p85: { label: "P85", color: "hsl(var(--chart-4))" },
  p97: { label: "P97", color: "hsl(var(--chart-5))" },
  patient: { label: "Paciente Actual", color: "hsl(var(--destructive))" },
  previousPatient: { label: "Paciente Anterior", color: "hsl(var(--chart-6))" }, // Assuming you have a chart-6 color
};

const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    const data = payload[0].payload;
    const unit = payload[0].dataKey.includes("weight") ? "kg" : "cm";
    const ageInMonths = parseInt(label);
    const ageDisplay = ageInMonths >= 24
      ? `${Math.floor(ageInMonths / 12)} años ${ageInMonths % 12} meses`
      : `${ageInMonths} meses`;

    return (
      <div className="p-2 bg-background border rounded-md shadow-lg">
        <p className="font-bold">{`Edad: ${ageDisplay}`}</p>
        {payload.map((p: any) => (
          <p key={p.name} style={{ color: p.color }}>
            {`${p.name}: ${p.value.toFixed(2)} ${unit}`}
          </p>
        ))}
         {data.patient && (
          <p style={{ color: 'red', fontWeight: 'bold' }}>
            {`Paciente: ${data.patient.toFixed(2)} ${unit}`}
          </p>
        )}
      </div>
    );
  }
  return null;
};


export function GrowthCharts({
  sex,
  dob,
  currentWeight,
  currentHeight,
  currentHeadCircumference,
  previousEvaluation,
}: GrowthChartsProps) {
  console.log("🔄 GrowthCharts component rendering...");
  console.log("GrowthCharts props:", { sex, dob, currentWeight, currentHeight, currentHeadCircumference, previousEvaluation });

  if (!sex || !dob) {
    return (
      <div className="text-center p-8 text-muted-foreground">
        <p>Para mostrar las curvas de crecimiento necesitas:</p>
        <ul className="list-disc list-inside mt-2">
          <li>Seleccionar el sexo del paciente</li>
          <li>Ingresar la fecha de nacimiento</li>
          <li>Ingresar al menos un valor (peso, talla o perímetro cefálico)</li>
        </ul>
      </div>
    );
  }

  const ageInMonths = differenceInMonths(new Date(), dob);
  const sexKey = sex === "femenino" ? "girls" : "boys";
  const percentileData = growthPercentiles[sexKey];

  const getPatientDataPoint = (value: string | number | undefined | null, date: Date) => {
    if (value === undefined || value === null) return null;
    const numericValue = typeof value === 'string' ? parseFloat(value) : value;
    if (isNaN(numericValue)) return null;
    const ageAtDate = differenceInMonths(date, dob);
    return { x: ageAtDate, y: numericValue };
  };

  const patientWeight = getPatientDataPoint(currentWeight, new Date());
  const patientHeight = getPatientDataPoint(currentHeight, new Date());
  const patientHC = getPatientDataPoint(currentHeadCircumference, new Date());

  const previousPatientWeight = previousEvaluation ? getPatientDataPoint(previousEvaluation.weight, new Date(previousEvaluation.createdAt)) : null;
  const previousPatientHeight = previousEvaluation ? getPatientDataPoint(previousEvaluation.height, new Date(previousEvaluation.createdAt)) : null;
  const previousPatientHC = previousEvaluation ? getPatientDataPoint(previousEvaluation.headCircumference, new Date(previousEvaluation.createdAt)) : null;

  const formatAge = (months: number) => {
    const years = Math.floor(months / 12);
    const remainingMonths = months % 12;
    if (years === 0) return `${months}m`;
    if (remainingMonths === 0) return `${years}a`;
    return `${years}a ${remainingMonths}m`;
  };

  const renderChart = (
    data: any[],
    yAxisLabel: string,
    currentPoint: { x: number; y: number } | null,
    previousPoint: { x: number; y: number } | null,
    title: string
  ) => {
    console.log("Rendering chart:", title, "Current point:", currentPoint, "Previous point:", previousPoint);

    const chartData = [...data];

    if (currentPoint) {
      const existingIndex = chartData.findIndex(d => d.age === currentPoint.x);
      if (existingIndex >= 0) {
        chartData[existingIndex].patient = currentPoint.y;
      } else {
        chartData.push({ age: currentPoint.x, patient: currentPoint.y });
      }
    }

    if (previousPoint) {
      const existingIndex = chartData.findIndex(d => d.age === previousPoint.x);
      if (existingIndex >= 0) {
        chartData[existingIndex].previousPatient = previousPoint.y;
      } else {
        chartData.push({ age: previousPoint.x, previousPatient: previousPoint.y });
      }
    }

    chartData.sort((a, b) => a.age - b.age);

    const allValues = chartData.flatMap(d => [d.p3, d.p15, d.p50, d.p85, d.p97, d.patient, d.previousPatient].filter(v => v !== null && v !== undefined));
    const yMin = Math.min(...allValues);
    const yMax = Math.max(...allValues);
    const yPadding = (yMax - yMin) * 0.05;
    const yDomain = [Math.max(0, yMin - yPadding), yMax + yPadding];

    return (
      <div className="h-96 w-full">
        <h4 className="text-lg font-semibold mb-4 text-center">{title}</h4>
        <ChartContainer config={chartConfig} className="w-full h-full">
          <ComposedChart data={chartData} margin={{ top: 30, right: 40, left: 40, bottom: 70 }}>
            <XAxis
              dataKey="age"
              type="number"
              scale="linear"
              tickFormatter={formatAge}
              label={{ value: 'Edad', position: 'insideBottom', offset: -10 }}
            />
            <YAxis
              domain={yDomain}
              label={{ value: yAxisLabel, angle: -90, position: 'insideLeft' }}
            />
            <Tooltip content={<CustomTooltip />} />
            <Legend />

            <Line type="monotone" dataKey="p3" stroke="hsl(var(--chart-1))" strokeWidth={1} dot={false} name="P3" connectNulls={false} />
            <Line type="monotone" dataKey="p15" stroke="hsl(var(--chart-2))" strokeWidth={1} dot={false} name="P15" connectNulls={false} />
            <Line type="monotone" dataKey="p50" stroke="hsl(var(--chart-3))" strokeWidth={2} dot={false} name="P50" connectNulls={false} />
            <Line type="monotone" dataKey="p85" stroke="hsl(var(--chart-4))" strokeWidth={1} dot={false} name="P85" connectNulls={false} />
            <Line type="monotone" dataKey="p97" stroke="hsl(var(--chart-5))" strokeWidth={1} dot={false} name="P97" connectNulls={false} />

            {currentPoint && (
              <Line
                type="monotone"
                dataKey="patient"
                stroke="transparent"
                strokeWidth={0}
                dot={{ fill: 'hsl(var(--destructive))', strokeWidth: 2, r: 5 }}
                name="Paciente Actual"
                connectNulls={false}
              />
            )}
            {previousPoint && (
              <Line
                type="monotone"
                dataKey="previousPatient"
                stroke="transparent"
                strokeWidth={0}
                dot={{ fill: 'hsl(var(--chart-6))', strokeWidth: 2, r: 5, strokeDasharray: '3 3' }}
                name="Paciente Anterior"
                connectNulls={false}
              />
            )}
          </ComposedChart>
        </ChartContainer>
      </div>
    );
  };

  return (
    <div className="space-y-4">
      <div className="text-center p-4 bg-blue-50 rounded-lg">
        <h3 className="font-semibold text-blue-900">Curvas de Crecimiento</h3>
        <p className="text-sm text-blue-700 mt-2">
          Edad: {ageInMonths} meses ({formatAge(ageInMonths)}) | Sexo: {sex}
        </p>
      </div>

      <Tabs defaultValue="weight">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="weight" disabled={!patientWeight}>Peso</TabsTrigger>
          <TabsTrigger value="height" disabled={!patientHeight}>Talla</TabsTrigger>
          <TabsTrigger value="hc" disabled={!patientHC || ageInMonths > 60}>
            Perímetro Cefálico {ageInMonths > 60 ? "(>5 años)" : ""}
          </TabsTrigger>
        </TabsList>
        <TabsContent value="weight">
          {patientWeight ? renderChart(percentileData.weight, "Peso (kg)", patientWeight, previousPatientWeight, "Peso para la Edad") :
            <div className="text-center p-8 text-muted-foreground">Ingrese el peso del paciente para ver el gráfico.</div>}
        </TabsContent>
        <TabsContent value="height">
          {patientHeight ? renderChart(percentileData.height, "Talla (cm)", patientHeight, previousPatientHeight, "Talla para la Edad") :
            <div className="text-center p-8 text-muted-foreground">Ingrese la talla del paciente para ver el gráfico.</div>}
        </TabsContent>
        <TabsContent value="hc">
          {patientHC && ageInMonths <= 60 ? renderChart(percentileData.headCircumference, "PC (cm)", patientHC, previousPatientHC, "Perímetro Cefálico para la Edad (0-5 años)") :
           <div className="text-center p-8 text-muted-foreground">
             {ageInMonths > 60 ? "El perímetro cefálico se mide rutinariamente solo hasta los 5 años." : "Ingrese el perímetro cefálico para ver el gráfico."}
           </div>}
        </TabsContent>
      </Tabs>
    </div>
  );
}


