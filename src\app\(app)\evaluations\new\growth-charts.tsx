
"use client";

import * as React from "react";
import { differenceInMonths } from "date-fns";
import {
  Line,
  XAxis,
  <PERSON>A<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Legend,
  ComposedChart,
} from "recharts";
import {
  ChartContainer,
} from "@/components/ui/chart";
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { growthPercentiles } from "@/lib/growth-chart-data";

interface GrowthChartsProps {
  sex?: "masculino" | "femenino" | string;
  dob?: Date;
  weight?: string;
  height?: string;
  headCircumference?: string;
}

const chartConfig = {
  p3: { label: "P3", color: "hsl(var(--chart-1))" },
  p15: { label: "P15", color: "hsl(var(--chart-2))" },
  p50: { label: "P50", color: "hsl(var(--chart-3))" },
  p85: { label: "P85", color: "hsl(var(--chart-4))" },
  p97: { label: "P97", color: "hsl(var(--chart-5))" },
  patient: { label: "Paciente", color: "hsl(var(--destructive))" },
};

const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    const data = payload[0].payload;
    const unit = payload[0].dataKey.includes("weight") ? "kg" : "cm";
    const ageInMonths = parseInt(label);
    const ageDisplay = ageInMonths >= 24
      ? `${Math.floor(ageInMonths / 12)} años ${ageInMonths % 12} meses`
      : `${ageInMonths} meses`;

    return (
      <div className="p-2 bg-background border rounded-md shadow-lg">
        <p className="font-bold">{`Edad: ${ageDisplay}`}</p>
        {payload.map((p: any) => (
          <p key={p.name} style={{ color: p.color }}>
            {`${p.name}: ${p.value.toFixed(2)} ${unit}`}
          </p>
        ))}
         {data.patient && (
          <p style={{ color: 'red', fontWeight: 'bold' }}>
            {`Paciente: ${data.patient.toFixed(2)} ${unit}`}
          </p>
        )}
      </div>
    );
  }
  return null;
};


export function GrowthCharts({
  sex,
  dob,
  weight,
  height,
  headCircumference,
}: GrowthChartsProps) {
  console.log("🔄 GrowthCharts component rendering...");
  console.log("GrowthCharts props:", { sex, dob, weight, height, headCircumference });

  if (!sex || !dob) {
    return (
      <div className="text-center p-8 text-muted-foreground">
        <p>Para mostrar las curvas de crecimiento necesitas:</p>
        <ul className="list-disc list-inside mt-2">
          <li>Seleccionar el sexo del paciente</li>
          <li>Ingresar la fecha de nacimiento</li>
          <li>Ingresar al menos un valor (peso, talla o perímetro cefálico)</li>
        </ul>
      </div>
    );
  }

  const ageInMonths = differenceInMonths(new Date(), dob);
  const sexKey = sex === "femenino" ? "girls" : "boys";
  const percentileData = growthPercentiles[sexKey];

  const getPatientDataPoint = (value: string | undefined) => {
    if (!value) return null;
    const numericValue = parseFloat(value);
    if (isNaN(numericValue)) return null;
    return { x: ageInMonths, y: numericValue };
  };

  const patientWeight = getPatientDataPoint(weight);
  const patientHeight = getPatientDataPoint(height);
  const patientHC = getPatientDataPoint(headCircumference);

  const formatAge = (months: number) => {
    const years = Math.floor(months / 12);
    const remainingMonths = months % 12;
    if (years === 0) return `${months}m`;
    if (remainingMonths === 0) return `${years}a`;
    return `${years}a ${remainingMonths}m`;
  };

  const renderChart = (
    data: any[],
    yAxisLabel: string,
    patientPoint: { x: number; y: number } | null,
    title: string
  ) => {
    console.log("Rendering chart:", title, "Patient point:", patientPoint);

    // Prepare chart data and add patient point
    const chartData = [...data];

    // Add patient data point to the chart data if it exists
    if (patientPoint) {
      // Find if there's already a data point for this age, or create a new one
      const existingPointIndex = chartData.findIndex(d => d.age === patientPoint.x);
      if (existingPointIndex >= 0) {
        // Add patient data to existing age point
        chartData[existingPointIndex] = {
          ...chartData[existingPointIndex],
          patient: patientPoint.y
        };
      } else {
        // Create new data point for patient age
        const newPoint = {
          age: patientPoint.x,
          p3: null,
          p15: null,
          p50: null,
          p85: null,
          p97: null,
          patient: patientPoint.y
        };
        chartData.push(newPoint);
        // Sort by age to maintain order
        chartData.sort((a, b) => a.age - b.age);
      }
    }

    return (
      <div className="h-96 w-full">
        <h4 className="text-lg font-semibold mb-4 text-center">{title}</h4>
        <ChartContainer config={chartConfig} className="w-full h-full">
          <ComposedChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 60 }}>
            <XAxis
              dataKey="age"
              domain={[0, 216]}
              type="number"
              scale="linear"
              tickFormatter={formatAge}
              label={{ value: 'Edad', position: 'insideBottom', offset: -10 }}
            />
            <YAxis
              label={{ value: yAxisLabel, angle: -90, position: 'insideLeft' }}
            />
            <Tooltip content={<CustomTooltip />} />
            <Legend />

            {/* Percentile lines */}
            <Line type="monotone" dataKey="p3" stroke="hsl(var(--chart-1))" strokeWidth={1} dot={false} name="P3" connectNulls={false} />
            <Line type="monotone" dataKey="p15" stroke="hsl(var(--chart-2))" strokeWidth={1} dot={false} name="P15" connectNulls={false} />
            <Line type="monotone" dataKey="p50" stroke="hsl(var(--chart-3))" strokeWidth={2} dot={false} name="P50" connectNulls={false} />
            <Line type="monotone" dataKey="p85" stroke="hsl(var(--chart-4))" strokeWidth={1} dot={false} name="P85" connectNulls={false} />
            <Line type="monotone" dataKey="p97" stroke="hsl(var(--chart-5))" strokeWidth={1} dot={false} name="P97" connectNulls={false} />

            {/* Patient data point */}
            {patientPoint && (
              <Line
                type="monotone"
                dataKey="patient"
                stroke="transparent"
                strokeWidth={0}
                dot={{ fill: '#dc2626', strokeWidth: 2, r: 5 }}
                name="Paciente"
                connectNulls={false}
              />
            )}
          </ComposedChart>
        </ChartContainer>
      </div>
    );
  };

  return (
    <div className="space-y-4">
      <div className="text-center p-4 bg-blue-50 rounded-lg">
        <h3 className="font-semibold text-blue-900">Curvas de Crecimiento</h3>
        <p className="text-sm text-blue-700 mt-2">
          Edad: {ageInMonths} meses ({formatAge(ageInMonths)}) | Sexo: {sex}
        </p>
      </div>

      <Tabs defaultValue="weight">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="weight" disabled={!patientWeight}>Peso</TabsTrigger>
          <TabsTrigger value="height" disabled={!patientHeight}>Talla</TabsTrigger>
          <TabsTrigger value="hc" disabled={!patientHC || ageInMonths > 60}>
            Perímetro Cefálico {ageInMonths > 60 ? "(>5 años)" : ""}
          </TabsTrigger>
        </TabsList>
        <TabsContent value="weight">
          {patientWeight ? renderChart(percentileData.weight, "Peso (kg)", patientWeight, "Peso para la Edad") :
            <div className="text-center p-8 text-muted-foreground">Ingrese el peso del paciente para ver el gráfico.</div>}
        </TabsContent>
        <TabsContent value="height">
          {patientHeight ? renderChart(percentileData.height, "Talla (cm)", patientHeight, "Talla para la Edad") :
            <div className="text-center p-8 text-muted-foreground">Ingrese la talla del paciente para ver el gráfico.</div>}
        </TabsContent>
        <TabsContent value="hc">
          {patientHC && ageInMonths <= 60 ? renderChart(percentileData.headCircumference, "PC (cm)", patientHC, "Perímetro Cefálico para la Edad (0-5 años)") :
           <div className="text-center p-8 text-muted-foreground">
             {ageInMonths > 60 ? "El perímetro cefálico se mide rutinariamente solo hasta los 5 años." : "Ingrese el perímetro cefálico para ver el gráfico."}
           </div>}
        </TabsContent>
      </Tabs>
    </div>
  );
}


