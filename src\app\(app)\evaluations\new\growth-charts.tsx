
"use client";

import * as React from "react";
import { differenceInMonths } from "date-fns";
import {
  Line,
  XAxis,
  <PERSON>A<PERSON>s,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  ReferenceDot,
  Composed<PERSON><PERSON>,
} from "recharts";
import {
  ChartContainer,
  ChartTooltipContent,
} from "@/components/ui/chart";
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { growthPercentiles } from "@/lib/growth-chart-data";

interface GrowthChartsProps {
  sex?: "masculino" | "femenino" | string;
  dob?: Date;
  weight?: string;
  height?: string;
  headCircumference?: string;
}

const chartConfig = {
  p3: { label: "P3", color: "hsl(var(--chart-1))" },
  p15: { label: "P15", color: "hsl(var(--chart-2))" },
  p50: { label: "P50", color: "hsl(var(--chart-3))" },
  p85: { label: "P85", color: "hsl(var(--chart-4))" },
  p97: { label: "P97", color: "hsl(var(--chart-5))" },
  patient: { label: "Paciente", color: "hsl(var(--destructive))" },
};

const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    const data = payload[0].payload;
    const unit = payload[0].dataKey.includes("weight") ? "kg" : "cm";
    return (
      <div className="p-2 bg-background border rounded-md shadow-lg">
        <p className="font-bold">{`Edad: ${label} meses`}</p>
        {payload.map((p: any) => (
          <p key={p.name} style={{ color: p.color }}>
            {`${p.name}: ${p.value.toFixed(2)} ${unit}`}
          </p>
        ))}
         {data.patient && (
          <p style={{ color: 'red', fontWeight: 'bold' }}>
            {`Paciente: ${data.patient.toFixed(2)} ${unit}`}
          </p>
        )}
      </div>
    );
  }
  return null;
};


export function GrowthCharts({
  sex,
  dob,
  weight,
  height,
  headCircumference,
}: GrowthChartsProps) {
  if (!sex || !dob) {
    return null;
  }

  const ageInMonths = differenceInMonths(new Date(), dob);
  const sexKey = sex === "femenino" ? "girls" : "boys";
  const percentileData = growthPercentiles[sexKey];

  const getPatientDataPoint = (value: string | undefined) => {
    if (!value) return null;
    const numericValue = parseFloat(value);
    if (isNaN(numericValue)) return null;
    return { x: ageInMonths, y: numericValue };
  };

  const patientWeight = getPatientDataPoint(weight);
  const patientHeight = getPatientDataPoint(height);
  const patientHC = getPatientDataPoint(headCircumference);

  const renderChart = (
    data: any[],
    yAxisLabel: string,
    patientDataPoint: { x: number; y: number } | null,
    title: string
  ) => {
    // Manually calculate Y-axis domain to ensure patient data is visible
    const allPValues = data.flatMap(d => [d.p3, d.p15, d.p50, d.p85, d.p97]);
    let yMin = Math.min(...allPValues);
    let yMax = Math.max(...allPValues);

    if (patientDataPoint) {
        yMin = Math.min(yMin, patientDataPoint.y);
        yMax = Math.max(yMax, patientDataPoint.y);
    }
    
    // Add some padding to the domain
    const yPadding = (yMax - yMin) * 0.1 || 1; // 10% padding, with a fallback
    const yAxisDomain = [Math.max(0, Math.floor(yMin - yPadding)), Math.ceil(yMax + yPadding)];

    return (
    <div className="h-[400px] w-full">
      <h3 className="text-center font-semibold mb-4">{title}</h3>
      <ChartContainer config={chartConfig} className="w-full h-full">
        <ComposedChart
          data={data}
          margin={{ top: 25, right: 25, left: 25, bottom: 25 }}
        >
            <defs>
                <linearGradient id="colorP3" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="hsl(var(--chart-1))" stopOpacity={0.8}/>
                <stop offset="95%" stopColor="hsl(var(--chart-1))" stopOpacity={0}/>
                </linearGradient>
                <linearGradient id="colorP97" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="hsl(var(--chart-5))" stopOpacity={0.8}/>
                <stop offset="95%" stopColor="hsl(var(--chart-5))" stopOpacity={0}/>
                </linearGradient>
            </defs>
          <XAxis
            dataKey="age"
            type="number"
            domain={[0, 24]}
            ticks={[0, 3, 6, 9, 12, 15, 18, 21, 24]}
          />
          <YAxis
            domain={yAxisDomain}
            allowDataOverflow
            label={{ value: yAxisLabel, angle: -90, position: "insideLeft", offset: 10 }}
          />
          <Tooltip content={<ChartTooltipContent />} />
          <Legend />
          
          <Line dataKey="p3" type="monotone" stroke="hsl(var(--chart-1))" strokeWidth={2} dot={false} name="P3" />
          <Line dataKey="p15" type="monotone" stroke="hsl(var(--chart-2))" strokeWidth={1.5} strokeDasharray="3 3" dot={false} name="P15" />
          <Line dataKey="p50" type="monotone" stroke="hsl(var(--chart-3))" strokeWidth={2} dot={false} name="P50" />
          <Line dataKey="p85" type="monotone" stroke="hsl(var(--chart-4))" strokeWidth={1.5} strokeDasharray="3 3" dot={false} name="P85" />
          <Line dataKey="p97" type="monotone" stroke="hsl(var(--chart-5))" strokeWidth={2} dot={false} name="P97" />

          {patientDataPoint && (
             <ReferenceDot
                x={patientDataPoint.x}
                y={patientDataPoint.y}
                r={6}
                fill="hsl(var(--destructive))"
                stroke="white"
                strokeWidth={2}
            />
          )}
        </ComposedChart>
      </ChartContainer>
    </div>
  )};

  return (
    <Tabs defaultValue="weight">
      <TabsList className="grid w-full grid-cols-3">
        <TabsTrigger value="weight" disabled={!patientWeight}>Peso</TabsTrigger>
        <TabsTrigger value="height" disabled={!patientHeight}>Talla</TabsTrigger>
        <TabsTrigger value="hc" disabled={!patientHC}>Perímetro Cefálico</TabsTrigger>
      </TabsList>
      <TabsContent value="weight">
        {patientWeight ? renderChart(percentileData.weight, "Peso (kg)", patientWeight, "Peso para la Edad") : <div className="text-center p-8 text-muted-foreground">Ingrese el peso del paciente para ver el gráfico.</div>}
      </TabsContent>
      <TabsContent value="height">
        {patientHeight ? renderChart(percentileData.height, "Talla (cm)", patientHeight, "Talla para la Edad") : <div className="text-center p-8 text-muted-foreground">Ingrese la talla del paciente para ver el gráfico.</div>}
      </TabsContent>
      <TabsContent value="hc">
        {patientHC ? renderChart(percentileData.headCircumference, "PC (cm)", patientHC, "Perímetro Cefálico para la Edad") : <div className="text-center p-8 text-muted-foreground">Ingrese el perímetro cefálico para ver el gráfico.</div>}
      </TabsContent>
    </Tabs>
  );
}
