"use client";

import * as React from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
  Sidebar,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
  SidebarProvider,
  SidebarInset,
  SidebarTrigger,
} from "@/components/ui/sidebar";
import {
  LayoutDashboard,
  Users,
  FilePlus2,
  Settings,
  FolderKanban,
  ChevronRight,
  FileText,
} from "lucide-react";
import { GastroKidEvalLogo } from "@/components/icons";
import { cn } from "@/lib/utils";

const menuItems = [
  { href: "/", label: "Tablero", icon: LayoutDashboard },
  { href: "/evaluations/new", label: "Nueva Evaluación", icon: FilePlus2 },
  { href: "/patients", label: "Registros de Pacientes", icon: Users },
  { href: "/admin", label: "Administración", icon: Settings },
];

export default function AppLayout({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();
  const [isDocumentsOpen, setIsDocumentsOpen] = React.useState(
    pathname.startsWith("/documents")
  );
  
  // Sync state with path changes
  React.useEffect(() => {
    setIsDocumentsOpen(pathname.startsWith("/documents"));
  }, [pathname]);

  return (
    <SidebarProvider>
      <Sidebar className="border-r">
        <SidebarHeader className="p-4">
          <div
            data-sidebar="header-content"
            className="flex items-center gap-2"
          >
            <div className="p-1.5 bg-primary/10 rounded-lg">
              <GastroKidEvalLogo className="size-6 text-primary" />
            </div>
            <span className="text-lg font-bold font-headline text-primary group-data-[collapsible=icon]:hidden">
              GastroKid Eval
            </span>
          </div>
        </SidebarHeader>
        <SidebarMenu className="p-2">
          {menuItems.map((item) => (
            <SidebarMenuItem key={item.href}>
              <Link href={item.href}>
                <SidebarMenuButton
                  isActive={pathname === item.href}
                  tooltip={item.label}
                  className="justify-start"
                >
                  <item.icon className="shrink-0" />
                  <span>{item.label}</span>
                </SidebarMenuButton>
              </Link>
            </SidebarMenuItem>
          ))}
          <SidebarMenuItem>
            <Collapsible
              open={isDocumentsOpen}
              onOpenChange={setIsDocumentsOpen}
            >
              <CollapsibleTrigger asChild>
                <SidebarMenuButton
                  isActive={pathname.startsWith("/documents")}
                  tooltip="Gestión Documental"
                  className="justify-between w-full"
                >
                  <div className="flex items-center gap-2">
                    <FolderKanban size={16} className="shrink-0" />
                    <span>Gestión Documental</span>
                  </div>
                  <ChevronRight
                    className={cn(
                      "h-4 w-4 shrink-0 transition-transform duration-200",
                      isDocumentsOpen && "rotate-90"
                    )}
                  />
                </SidebarMenuButton>
              </CollapsibleTrigger>
              <CollapsibleContent className="pl-6 pt-1">
                <SidebarMenu className="border-l border-border/50">
                  <SidebarMenuItem className="ml-2">
                    <Link href="/documents/medical-certificate">
                      <SidebarMenuButton
                        isActive={
                          pathname === "/documents/medical-certificate"
                        }
                        tooltip="Certificado Médico"
                        className="justify-start h-8"
                        variant="ghost"
                      >
                        <FileText className="shrink-0" />
                        <span>Certificado Médico</span>
                      </SidebarMenuButton>
                    </Link>
                  </SidebarMenuItem>
                </SidebarMenu>
              </CollapsibleContent>
            </Collapsible>
          </SidebarMenuItem>
        </SidebarMenu>
      </Sidebar>
      <SidebarInset>
        <div className="p-4 md:hidden">
          <SidebarTrigger />
        </div>
        <main className="flex-1 p-4 md:p-6 lg:p-8">{children}</main>
      </SidebarInset>
    </SidebarProvider>
  );
}
