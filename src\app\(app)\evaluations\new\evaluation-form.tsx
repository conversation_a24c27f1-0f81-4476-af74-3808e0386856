
"use client";

import * as React from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  format,
  differenceInYears,
  differenceInMonths,
  differenceInDays,
  subYears,
} from "date-fns";
import { es } from "date-fns/locale";

import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Textarea } from "@/components/ui/textarea";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { cn } from "@/lib/utils";
import {
  CalendarIcon,
  Upload,
  FileText,
  User,
  Users2,
  Stethoscope,
  TestTube,
  X,
  LineChart,
} from "lucide-react";
import { toast } from "@/hooks/use-toast";
import { useSession } from "@/hooks/use-session";
import { evaluationSchema } from "./schemas";
import { Label } from "@/components/ui/label";
import { GrowthCharts } from "./growth-charts";
import { EvaluationPreview } from "./evaluation-preview";
import { EvaluationResultModal } from "./evaluation-result-modal";


import { type Patient, type Evaluation as PrismaEvaluation, type EvaluationLabExam } from "@prisma/client";

type UploadedFile = { name: string; category: string; url: string };

type PopulatedEvaluation = PrismaEvaluation & {
  evaluationLabExams: EvaluationLabExam[];
};

interface EvaluationFormProps {
  patientData?: Patient;
  previousEvaluationData?: PopulatedEvaluation | null;
}

export function EvaluationForm({ patientData, previousEvaluationData }: EvaluationFormProps) {
  const { user } = useSession();
  const form = useForm<z.infer<typeof evaluationSchema>>({
    resolver: zodResolver(evaluationSchema),
    defaultValues: {
      appointment: {
        date: new Date(),
        time: "",
        recordNumber: patientData?.recordNumber || "",
        insurance: "",
        pediatrician: "",
        referrer: "",
      },
      patient: {
        name: patientData?.name || "",
        idCard: patientData?.idNumber || "",
        gender: patientData?.gender || "",
        dob: patientData ? new Date(patientData.dob) : undefined,
        age: "",
      },
      mother: {
        name: "",
        age: "",
        address: "",
        phone: "",
        occupation: "",
      },
      father: {
        name: "",
        age: "",
        address: "",
        phone: "",
        occupation: "",
      },
      medical: {
        consultationReason: "",
        currentIllness: "",
        upperDigestiveSymptoms: "",
        lowerDigestiveSymptoms: "",
        bowelHabits: "",
        weight: "",
        height: "",
        headCircumference: "",
        bloodPressure: "",
        temperature: "",
        cardiacFrequency: "",
        oxygenSaturation: "",
        perinatalHistory: "",
        nutritionalHistory: "",
        developmentHistory: "",
        immunizations: "",
        personalMedicalHistory: "",
        familyMedicalHistory: "",
        generalObservations: "",
        systemsReview: "",
        physicalExam: "",
        paraclinical: "",
        diagnosticImpression: "",
        actionPlan: "",
      },
      labFiles: [],
    },
  });


  const { setValue } = form;
  const [fileCategory, setFileCategory] = React.useState(
    "Examen de Laboratorio (PDF)"
  );
  const [selectedFile, setSelectedFile] = React.useState<File | null>(null);
  const [uploadedFiles, setUploadedFiles] = React.useState<UploadedFile[]>([]);
  const [submittedData, setSubmittedData] =
    React.useState<z.infer<typeof evaluationSchema> | null>(null);

  const dob = form.watch("patient.dob");
  const watchedData = form.watch();

  React.useEffect(() => {
    if (dob) {
      const now = new Date();
      if (dob > now) {
        form.setValue("patient.age", "Fecha inválida");
        return;
      }

      const years = differenceInYears(now, dob);
      const dateAfterYears = subYears(now, years);
      const months = differenceInMonths(dateAfterYears, dob);

      let ageString = "";
      if (years > 0) {
        ageString += `${years} año${years > 1 ? "s" : ""}`;
      }
      if (months > 0) {
        ageString += `${ageString ? ", " : ""}${months} mes${
          months > 1 ? "es" : ""
        }`;
      }

      if (years === 0 && months === 0) {
        const days = differenceInDays(now, dob);
        if (days === 0) {
          ageString = "Recién nacido";
        } else {
          ageString = `${days} día${days > 1 ? "s" : ""}`;
        }
      }

      if (!ageString) {
        ageString = "Recién nacido";
      }

      form.setValue("patient.age", ageString);
    } else {
      form.setValue("patient.age", "");
    }
  }, [dob, form, setValue]);

  React.useEffect(() => {
    setValue("labFiles", uploadedFiles);
  }, [uploadedFiles, setValue]);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files.length > 0) {
      setSelectedFile(event.target.files[0]);
    } else {
      setSelectedFile(null);
    }
  };

  const [isUploading, setIsUploading] = React.useState(false);

  const handleFileUpload = async () => {
    if (selectedFile) {
      setIsUploading(true);
      const formData = new FormData();
      formData.append("file", selectedFile);

      try {
        const response = await fetch("/api/upload", {
          method: "POST",
          body: formData,
        });

        const result = await response.json();

        if (result.success) {
          const newFile: UploadedFile = {
            name: selectedFile.name,
            category: fileCategory,
            url: result.url,
          };
          setUploadedFiles((prevFiles) => [...prevFiles, newFile]);
          toast({ title: "Archivo subido con éxito" });
        } else {
          toast({ title: "Error al subir el archivo", description: result.error, variant: "destructive" });
        }
      } catch (error) {
        toast({ title: "Error de red", description: "No se pudo conectar con el servidor.", variant: "destructive" });
      } finally {
        setSelectedFile(null);
        const fileInput = document.getElementById(
          "labFileInput"
        ) as HTMLInputElement;
        if (fileInput) {
          fileInput.value = "";
        }
        setIsUploading(false);
      }
    }
  };

  const handleFileRemove = (indexToRemove: number) => {
    setUploadedFiles((prevFiles) =>
      prevFiles.filter((_, index) => index !== indexToRemove)
    );
  };

  const [isSubmitting, setIsSubmitting] = React.useState(false);

  async function onSubmit(values: z.infer<typeof evaluationSchema>) {
    console.log("Guardando evaluación...");
    setIsSubmitting(true);

    if (!user) {
      toast({
        title: "Error de Autenticación",
        description: "Debe estar autenticado para guardar una evaluación.",
        variant: "destructive",
      });
      setIsSubmitting(false);
      return;
    }

    const userId = parseInt(user.userId);

    try {
      const response = await fetch("/api/evaluations", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ ...values, userId }),
      });

      if (response.ok) {
        const newEvaluation = await response.json();
        setSubmittedData(values); // Keep this for the preview modal
        toast({
          title: "Evaluación Guardada con Éxito",
          description: `Se ha creado la evaluación con el ID: ${newEvaluation.id}`,
        });
        // Optionally, reset the form or redirect the user
        form.reset();
        setUploadedFiles([]);
      } else {
        const errorData = await response.json();
        toast({
          title: "Error al Guardar la Evaluación",
          description: errorData.error || "Ocurrió un error inesperado.",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error de Red",
        description: "No se pudo conectar con el servidor para guardar la evaluación.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  }

  const showGrowthCharts =
    watchedData.patient.gender &&
    watchedData.patient.dob &&
    (watchedData.medical.weight ||
      watchedData.medical.height ||
      watchedData.medical.headCircumference);

  console.log("showGrowthCharts:", showGrowthCharts);
  console.log("watchedData:", {
    gender: watchedData.patient.gender,
    dob: watchedData.patient.dob,
    weight: watchedData.medical.weight,
    height: watchedData.medical.height,
    headCircumference: watchedData.medical.headCircumference
  });

  // Mostrar mensaje si no está autenticado
  if (!user) {
    return (
      <div className="max-w-md mx-auto mt-8 p-6 bg-yellow-50 border border-yellow-200 rounded-lg">
        <h2 className="text-lg font-semibold text-yellow-800 mb-2">🔐 Autenticación Requerida</h2>
        <p className="text-yellow-700 mb-4">
          Debe iniciar sesión para crear una evaluación.
        </p>
        <a
          href="/login"
          className="inline-block bg-yellow-600 text-white px-4 py-2 rounded hover:bg-yellow-700 transition-colors"
        >
          Ir a Iniciar Sesión
        </a>
      </div>
    );
  }

  return (
    <Form {...form}>
      {/* Off-screen preview for PDF generation */}
      <div className="absolute top-0 left-[-9999px] z-[-1] opacity-0">
        {submittedData && <EvaluationPreview data={submittedData} />}
      </div>

      {/* Result modal */}
      <EvaluationResultModal
        isOpen={!!submittedData}
        onOpenChange={(isOpen) => {
          if (!isOpen) {
            setSubmittedData(null);
          }
        }}
        data={submittedData}
      />

      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        {/* Section 1: Cita y Expediente */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="text-primary" />
              Información de Cita y Expediente
            </CardTitle>
            <CardDescription>
              Registre los detalles administrativos de la cita del paciente.
            </CardDescription>
          </CardHeader>
          <CardContent className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <FormField
              control={form.control}
              name="appointment.date"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Fecha de Cita*</FormLabel>
                  <Popover>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant={"outline"}
                          className={cn(
                            "pl-3 text-left font-normal",
                            !field.value && "text-muted-foreground"
                          )}
                        >
                          {field.value ? (
                            format(field.value, "PPP", { locale: es })
                          ) : (
                            <span>Seleccionar fecha</span>
                          )}
                          <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={field.value}
                        onSelect={field.onChange}
                        disabled={(date) =>
                          date > new Date() || date < new Date("1900-01-01")
                        }
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="appointment.time"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Hora de la Cita*</FormLabel>
                  <FormControl>
                    <Input type="time" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="appointment.recordNumber"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nº de Expediente*</FormLabel>
                  <FormControl>
                    <Input placeholder="Ej: P001-2024" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="appointment.insurance"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nombre del Seguro</FormLabel>
                  <FormControl>
                    <Input placeholder="Seguro ABC" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="appointment.pediatrician"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nombre del Pediatra</FormLabel>
                  <FormControl>
                    <Input placeholder="Dr. Juan Pérez" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="appointment.referrer"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Persona que Refiere</FormLabel>
                  <FormControl>
                    <Input placeholder="Nombre de quien refiere" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        {/* Section 2: Datos del Paciente */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="text-primary" />
              Datos del Paciente
            </CardTitle>
            <CardDescription>
              Información personal y demográfica del paciente.
            </CardDescription>
          </CardHeader>
          <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField
              control={form.control}
              name="patient.name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nombre Completo*</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Nombres y apellidos del paciente"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="patient.idCard"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nº de Cédula</FormLabel>
                  <FormControl>
                    <Input placeholder="Número de cédula" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 md:col-span-2">
              <FormField
                control={form.control}
                name="patient.dob"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Fecha de Nacimiento*</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant={"outline"}
                            className={cn(
                              "w-full justify-between pl-3 text-left font-normal",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            {field.value ? (
                              format(field.value, "PPP", { locale: es })
                            ) : (
                              <span>Seleccionar fecha</span>
                            )}
                            <CalendarIcon className="h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          captionLayout="dropdown-buttons"
                          fromYear={1950}
                          toYear={new Date().getFullYear()}
                          disabled={(date) =>
                            date > new Date() || date < new Date("1950-01-01")
                          }
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="patient.age"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Edad Calculada</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Automático"
                        {...field}
                        readOnly
                        className="bg-slate-100 dark:bg-slate-800"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="patient.gender"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Sexo*</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Seleccionar sexo" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="masculino">Masculino</SelectItem>
                        <SelectItem value="femenino">Femenino</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </CardContent>
        </Card>

        {/* Section 3: Datos de los Padres */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users2 className="text-primary" />
              Datos de los Padres
            </CardTitle>
            <CardDescription>
              Información de contacto y demográfica de los padres del paciente.
            </CardDescription>
          </CardHeader>
          <CardContent className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div className="space-y-4">
              <h3 className="font-semibold text-lg">Detalles de la Madre</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="mother.name"
                  render={({ field }) => (
                    <FormItem className="md:col-span-2">
                      <FormLabel>Nombre Completo*</FormLabel>
                      <FormControl>
                        <Input placeholder="Nombres y apellidos" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="mother.age"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Edad</FormLabel>
                      <FormControl>
                        <Input type="number" placeholder="Edad" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="mother.phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Número de Teléfono</FormLabel>
                      <FormControl>
                        <Input placeholder="(*************" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="mother.address"
                  render={({ field }) => (
                    <FormItem className="md:col-span-2">
                      <FormLabel>Dirección</FormLabel>
                      <FormControl>
                        <Input placeholder="Dirección completa" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="mother.occupation"
                  render={({ field }) => (
                    <FormItem className="md:col-span-2">
                      <FormLabel>Ocupación</FormLabel>
                      <FormControl>
                        <Input placeholder="Ocupación" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
            <div className="space-y-4">
              <h3 className="font-semibold text-lg">Detalles del Padre</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="father.name"
                  render={({ field }) => (
                    <FormItem className="md:col-span-2">
                      <FormLabel>Nombre Completo*</FormLabel>
                      <FormControl>
                        <Input placeholder="Nombres y apellidos" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="father.age"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Edad</FormLabel>
                      <FormControl>
                        <Input type="number" placeholder="Edad" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="father.phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Número de Teléfono</FormLabel>
                      <FormControl>
                        <Input placeholder="(*************" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="father.address"
                  render={({ field }) => (
                    <FormItem className="md:col-span-2">
                      <FormLabel>Dirección</FormLabel>
                      <FormControl>
                        <Input placeholder="Dirección completa" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="father.occupation"
                  render={({ field }) => (
                    <FormItem className="md:col-span-2">
                      <FormLabel>Ocupación</FormLabel>
                      <FormControl>
                        <Input placeholder="Ocupación" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Section 4: Evaluación Médica */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Stethoscope className="text-primary" />
              Evaluación Médica
            </CardTitle>
            <CardDescription>
              Detalle clínico, examen físico e historial del paciente.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-4">
              <h3 className="font-semibold text-lg">Información Clínica</h3>
              <FormField
                control={form.control}
                name="medical.consultationReason"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Motivo de Consulta*</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Describa el motivo principal de la visita..."
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="medical.currentIllness"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Descripción de la Enfermedad Actual*</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Evolución y síntomas de la condición actual..."
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="medical.upperDigestiveSymptoms"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Síntomas Digestivos Superiores</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Náuseas, vómitos, reflujo..."
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="medical.lowerDigestiveSymptoms"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Síntomas Digestivos Inferiores</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Distensión, dolor abdominal, flatulencias..."
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <FormField
                control={form.control}
                name="medical.bowelHabits"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Hábitos Intestinales</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Frecuencia, consistencia, color de las deposiciones..."
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="space-y-4">
              <h3 className="font-semibold text-lg">
                Datos Antropométricos y Mediciones
              </h3>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                <FormField
                  control={form.control}
                  name="medical.weight"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Peso (kg)</FormLabel>
                      <FormControl>
                        <Input type="number" placeholder="kg" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="medical.height"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Talla (cm)</FormLabel>
                      <FormControl>
                        <Input type="number" placeholder="cm" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="medical.headCircumference"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Perímetro Cefálico (cm)</FormLabel>
                      <FormControl>
                        <Input type="number" placeholder="cm" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="medical.temperature"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Temperatura (°C)</FormLabel>
                      <FormControl>
                        <Input type="number" placeholder="°C" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="medical.cardiacFrequency"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Frec. Cardiaca (lpm)</FormLabel>
                      <FormControl>
                        <Input type="number" placeholder="lpm" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="medical.bloodPressure"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Presión Arterial</FormLabel>
                      <FormControl>
                        <Input placeholder="e.g. 120/80" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="medical.oxygenSaturation"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Sat. de Oxígeno (%)</FormLabel>
                      <FormControl>
                        <Input type="number" placeholder="SpO2 %" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <Accordion type="multiple" className="w-full space-y-4">
              <AccordionItem
                value="history"
                className="border rounded-md px-4 bg-white/50"
              >
                <AccordionTrigger className="py-3 font-semibold text-lg hover:no-underline">
                  Historia del Paciente
                </AccordionTrigger>
                <AccordionContent className="space-y-4 pt-4">
                  <FormField
                    control={form.control}
                    name="medical.perinatalHistory"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Historia Perinatal</FormLabel>
                        <FormControl>
                          <Textarea {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="medical.nutritionalHistory"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Historia Nutricional</FormLabel>
                        <FormControl>
                          <Textarea {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="medical.developmentHistory"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Historia del Desarrollo</FormLabel>
                        <FormControl>
                          <Textarea {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="medical.immunizations"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Inmunizaciones</FormLabel>
                        <FormControl>
                          <Textarea {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="medical.personalMedicalHistory"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Antecedentes Médicos Personales</FormLabel>
                        <FormControl>
                          <Textarea {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="medical.familyMedicalHistory"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Antecedentes Médicos Familiares</FormLabel>
                        <FormControl>
                          <Textarea {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </AccordionContent>
              </AccordionItem>
              <AccordionItem
                value="exam"
                className="border rounded-md px-4 bg-white/50"
              >
                <AccordionTrigger className="py-3 font-semibold text-lg hover:no-underline">
                  Examen y Plan
                </AccordionTrigger>
                <AccordionContent className="space-y-4 pt-4">
                  <FormField
                    control={form.control}
                    name="medical.generalObservations"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Observaciones Generales</FormLabel>
                        <FormControl>
                          <Textarea {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="medical.systemsReview"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Revisión Actual por Sistemas</FormLabel>
                        <FormControl>
                          <Textarea {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="medical.physicalExam"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Objetivo (Examen Físico)</FormLabel>
                        <FormControl>
                          <Textarea {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="medical.paraclinical"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Exámenes Paraclínicos</FormLabel>
                        <FormControl>
                          <Textarea {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="medical.diagnosticImpression"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Impresiones Diagnósticas</FormLabel>
                        <FormControl>
                          <Textarea {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="medical.actionPlan"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Plan de Acción</FormLabel>
                        <FormControl>
                          <Textarea {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          </CardContent>
        </Card>

        {/* Section 5: Growth Charts */}
        {showGrowthCharts && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <LineChart className="text-primary" />
                Curvas de Crecimiento (OMS)
              </CardTitle>
              <CardDescription>
                Visualización de percentiles de peso, talla y perímetro
                cefálico para la edad.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <GrowthCharts
                sex={watchedData.patient.gender}
                dob={watchedData.patient.dob}
                currentWeight={watchedData.medical.weight}
                currentHeight={watchedData.medical.height}
                currentHeadCircumference={watchedData.medical.headCircumference}
                previousEvaluation={previousEvaluationData}
              />
            </CardContent>
          </Card>
        )}

        {/* Section 6: Exámenes de Laboratorio */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TestTube className="text-primary" />
              Exámenes de Laboratorio
            </CardTitle>
            <CardDescription>
              Subir y gestionar resultados de exámenes de laboratorio (PDF) y
              estudios de imagen (imágenes).
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              <div className="bg-slate-50 dark:bg-slate-900/50 p-4 rounded-lg border">
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-[2fr_3fr_1fr] gap-4 items-end">
                  <div className="space-y-1.5">
                    <Label>Categoría de Archivo</Label>
                    <Select
                      onValueChange={setFileCategory}
                      value={fileCategory}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Seleccionar categoría" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Examen de Laboratorio (PDF)">
                          Examen de Laboratorio (PDF)
                        </SelectItem>
                        <SelectItem value="Estudio de Imagen (Imágenes)">
                          Estudio de Imagen (Imágenes)
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-1.5">
                    <Label htmlFor="labFileInput">Seleccionar Archivo</Label>
                    <div className="flex h-10 w-full items-center rounded-md border border-input bg-background">
                      <Button
                        type="button"
                        variant="ghost"
                        asChild
                        className="relative h-full rounded-r-none border-r"
                      >
                        <label
                          htmlFor="labFileInput"
                          className="cursor-pointer flex items-center px-3"
                        >
                          Seleccionar archivo
                          <Input
                            id="labFileInput"
                            type="file"
                            className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                            onChange={handleFileSelect}
                            accept=".pdf,.jpg,.jpeg,.png"
                          />
                        </label>
                      </Button>
                      <p className="text-sm text-muted-foreground flex-1 truncate px-3">
                        {selectedFile?.name ?? "Ningún archivo seleccionado"}
                      </p>
                    </div>
                  </div>

                  <Button
                    type="button"
                    onClick={handleFileUpload}
                    disabled={!selectedFile}
                    className="w-full"
                  >
                    <Upload className="mr-2 h-4 w-4" />
                    Subir Archivo
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                {uploadedFiles.length === 0 ? (
                  <div className="text-center text-muted-foreground py-10 border-2 border-dashed rounded-md">
                    <p>Aún no se han subido archivos para esta evaluación.</p>
                  </div>
                ) : (
                  <ul className="divide-y rounded-md border">
                    {uploadedFiles.map((item, index) => (
                      <li
                        key={index}
                        className="flex items-center justify-between p-3"
                      >
                        <div className="flex items-center gap-3">
                          <FileText className="h-6 w-6 text-muted-foreground" />
                          <div>
                            <p className="font-medium text-sm">
                              {item.name}
                            </p>
                            <p className="text-xs text-muted-foreground">
                              {item.category}
                            </p>
                          </div>
                        </div>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleFileRemove(index)}
                        >
                          <X className="h-4 w-4 text-red-500" />
                          <span className="sr-only">Remove file</span>
                        </Button>
                      </li>
                    ))}
                  </ul>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="flex justify-end gap-4">
          <Button
            type="submit"
            disabled={isSubmitting}
            className="bg-blue-600 hover:bg-blue-700 text-white font-semibold px-6 py-2"
          >
            {isSubmitting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Guardando...
              </>
            ) : (
              "Guardar Evaluación"
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
}
