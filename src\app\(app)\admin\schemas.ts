
import { z } from "zod";

export const userSchema = z.object({
  name: z.string().min(3, { message: "El nombre debe tener al menos 3 caracteres." }),
  email: z.string().email({ message: "Por favor ingrese un correo válido." }),
  role: z.string().min(1, { message: "El rol es requerido." }),
  specialty: z.string().min(1, { message: "La especialidad es requerida." }),
  msp: z.string().min(1, { message: "El N° MSP es requerido." }),
  phone: z.string().min(1, { message: "El teléfono es requerido." }),
});
