import { mockPatients, type Patient } from "@/lib/mock-data";
import { PatientTable } from "./client-page";
import { Card, CardContent } from "@/components/ui/card";

async function getPatients(): Promise<Patient[]> {
  // In a real app, you'd fetch this data from your database.
  return Promise.resolve(mockPatients);
}

export default async function PatientsPage() {
  const patients = await getPatients();

  return (
    <div className="space-y-4">
      <div>
        <h1 className="text-2xl font-bold font-headline">Pacientes Registrados</h1>
        <p className="text-muted-foreground mt-1">
          Lista de todos los pacientes en el sistema
        </p>
      </div>
      <Card>
        <CardContent className="p-4 sm:p-6">
          <PatientTable data={patients} />
        </CardContent>
      </Card>
    </div>
  );
}