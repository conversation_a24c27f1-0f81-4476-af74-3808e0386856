import { NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { evaluationSchema } from '@/app/(app)/evaluations/new/schemas';

const prisma = new PrismaClient();

export async function POST(request: Request) {
  try {
    const body = await request.json();
    console.log('Received body:', JSON.stringify(body, null, 2));

    const validation = evaluationSchema.safeParse(body);

    if (!validation.success) {
      console.log('Validation errors:', validation.error.flatten());
      return NextResponse.json({
        error: 'Invalid input',
        details: validation.error.flatten(),
        receivedData: body
      }, { status: 400 });
    }

    const { patient, appointment, medical, labFiles, userId } = validation.data;

    // For now, we'll find the patient by their ID card number (cédula).
    // In a real app, you might have a more robust way of identifying patients.
    let existingPatient = await prisma.patient.findUnique({
      where: { alias: patient.idCard },
    });

    if (!existingPatient) {
      // If the patient doesn't exist, create them.
      existingPatient = await prisma.patient.create({
        data: {
          alias: patient.idCard,
          name: patient.name,
          // dob, gender, etc. should be added to the patient model
        },
      });
    }

    const evaluation = await prisma.evaluation.create({
      data: {
        alias: `eval-${Date.now()}`,
        reason: medical.consultationReason,
        doctor: {
          connect: { id: userId },
        },
        patient: {
          connect: { id: existingPatient.id },
        },
        evaluationLabExams: {
          create: labFiles?.map((file) => ({
            name: file.name,
            category: file.category,
            url: file.url,
          })) || [],
        },
        // Medical Data
        currentIllness: medical.currentIllness,
        upperDigestiveSymptoms: medical.upperDigestiveSymptoms,
        lowerDigestiveSymptoms: medical.lowerDigestiveSymptoms,
        bowelHabits: medical.bowelHabits,
        weight: medical.weight,
        height: medical.height,
        headCircumference: medical.headCircumference,
        bloodPressure: medical.bloodPressure,
        temperature: medical.temperature,
        cardiacFrequency: medical.cardiacFrequency,
        oxygenSaturation: medical.oxygenSaturation,
        perinatalHistory: medical.perinatalHistory,
        nutritionalHistory: medical.nutritionalHistory,
        developmentHistory: medical.developmentHistory,
        immunizations: medical.immunizations,
        personalMedicalHistory: medical.personalMedicalHistory,
        familyMedicalHistory: medical.familyMedicalHistory,
        generalObservations: medical.generalObservations,
        systemsReview: medical.systemsReview,
        physicalExam: medical.physicalExam,
        paraclinical: medical.paraclinical,
        diagnosticImpression: medical.diagnosticImpression,
        actionPlan: medical.actionPlan,
      },
    });

    return NextResponse.json(evaluation, { status: 201 });

  } catch (error) {
    console.error('Error creating evaluation:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
