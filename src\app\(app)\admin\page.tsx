
import { type User } from "@/lib/mock-data";
import { UserTable } from "./client-page";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { AddUserDialog } from "./add-user-dialog";
import { firestore } from "@/lib/firebase/admin";

async function getUsers(): Promise<User[]> {
  try {
    const usersSnapshot = await firestore.collection("users").get();
    if (usersSnapshot.empty) {
      return [];
    }

    const users: User[] = usersSnapshot.docs.map((doc) => {
      const data = doc.data();
      return {
        id: doc.id,
        name: data.name,
        email: data.email,
        role: data.role,
        specialty: data.specialty,
        msp: data.msp,
        phone: data.phone,
      };
    });

    return users;
  } catch (error) {
    console.error("Error fetching users:", error);
    // In case of an error, return an empty array to prevent the page from crashing.
    return [];
  }
}

export default async function AdminPage() {
  const users = await getUsers();

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between sm:items-center gap-4">
        <h1 className="text-2xl font-bold font-headline">Gestión de Usuarios</h1>
        <AddUserDialog />
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Usuarios Registrados</CardTitle>
          <CardDescription>
            Una lista de todos los usuarios médicos en el sistema.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <UserTable data={users} />
        </CardContent>
      </Card>
    </div>
  );
}
